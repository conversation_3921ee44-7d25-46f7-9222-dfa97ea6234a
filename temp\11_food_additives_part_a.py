#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Taylor & Francis Food Additives期刊文章抓取脚本
使用DrissionPage抓取https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20页面的文章数据
"""

from DrissionPage import ChromiumPage
import time
import json
from urllib.parse import urljoin


def extract_article_info(page):
    """
    提取页面中的文章信息
    """
    articles = []

    # 查找所有文章条目
    article_elements = page.eles('.tocArticleEntry.include-metrics-panel.toc-article-tools')

    print(f"找到 {len(article_elements)} 篇文章")

    for article in article_elements:
        try:
            # 提取标题和链接
            title_element = article.ele('.art_title.linkable a .hlFld-Title', timeout=2)
            if title_element:
                title = title_element.text.strip()
                # 获取链接
                link_element = article.ele('.art_title.linkable a', timeout=2)
                if link_element:
                    relative_link = link_element.attr('href')
                    full_link = urljoin('https://www.tandfonline.com', relative_link)
                else:
                    full_link = ""
            else:
                title = ""
                full_link = ""

            # 提取发布日期
            date_element = article.ele('.tocEPubDate .date', timeout=2)
            if date_element:
                publish_date = date_element.text.strip()
            else:
                publish_date = ""

            # 提取作者信息
            authors = []
            author_elements = article.eles('.tocAuthors .entryAuthor', timeout=2)
            for author_elem in author_elements:
                if author_elem:
                    authors.append(author_elem.text.strip())

            # 提取文章类型
            article_type_element = article.ele('.article-type', timeout=2)
            if article_type_element:
                article_type = article_type_element.text.strip()
            else:
                article_type = ""

            if title:  # 只有当标题存在时才添加文章
                article_info = {
                    'title': title,
                    'publish_date': publish_date,
                    'link': full_link,
                    'authors': authors,
                    'article_type': article_type
                }
                articles.append(article_info)

                print(f"提取文章: {title}")
                print(f"日期: {publish_date}")
                print(f"链接: {full_link}")
                print("-" * 80)

        except Exception as e:
            print(f"提取文章信息时出错: {e}")
            continue

    return articles


def main():
    """
    主函数
    """
    url = "https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20"

    # 创建页面对象
    page = ChromiumPage()

    try:
        print(f"正在访问: {url}")
        page.get(url)

        # 等待页面加载
        print("等待页面加载...")
        time.sleep(3)

        # 检查页面是否正确加载
        if "Taylor & Francis" in page.title:
            print(f"页面加载成功: {page.title}")
        else:
            print(f"页面标题: {page.title}")

        # 等待文章列表加载
        try:
            page.wait.ele_loaded('.tocArticleEntry', timeout=10)
            print("文章列表加载完成")
        except:
            print("等待文章列表超时，继续尝试提取...")

        # 提取文章信息
        articles = extract_article_info(page)

        if articles:
            print(f"\n成功提取 {len(articles)} 篇文章信息")

            # 保存到JSON文件
            output_file = "food_additives_articles.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)

            print(f"文章信息已保存到: {output_file}")

            # 显示第一篇文章作为示例
            if articles:
                print("\n第一篇文章示例:")
                first_article = articles[0]
                print(f"标题: {first_article['title']}")
                print(f"日期: {first_article['publish_date']}")
                print(f"链接: {first_article['link']}")
                print(f"作者: {', '.join(first_article['authors'])}")
                print(f"类型: {first_article['article_type']}")
        else:
            print("未找到任何文章信息")

    except Exception as e:
        print(f"抓取过程中出错: {e}")

    finally:
        # 关闭浏览器
        page.quit()


if __name__ == "__main__":
    main()

