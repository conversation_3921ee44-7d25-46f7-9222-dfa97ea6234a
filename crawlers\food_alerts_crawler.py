#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英国食品标准局API数据抓取爬虫
抓取https://www.food.gov.uk/search-api的食品警报数据
"""

import time
import logging
from datetime import datetime, timedelta
import urllib3
import json
from crawlers.base_crawler import BaseCrawler
from models.data_general import DataGeneral
from config import Config

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FoodAlertsCrawler(BaseCrawler):
    """
    英国食品标准局食品警报数据爬虫
    """
    
    def __init__(self, task_id, batch_id=None, days=None):
        """
        初始化爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
            days (int, optional): 抓取最近多少天的数据，默认使用配置值
        """
        super().__init__(task_id, batch_id)
        self.days = days or Config.CAPTURE_DAYS
        self.logger = logging.getLogger("crawler.FoodAlertsCrawler")
        
        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.food.gov.uk/',
        })
        
        # 禁用SSL验证
        self.session.verify = False
        
        # API基础URL
        self.base_url = "https://www.food.gov.uk/search-api"
    
    def _crawl(self):
        """
        执行抓取逻辑
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            # 抓取食品警报数据
            self.logger.info("开始抓取英国食品标准局食品警报数据")
            food_alerts = self.scrape_food_alerts()
            
            if food_alerts:
                # 将数据转换为DataGeneral模型并保存
                records = self.convert_to_data_general(food_alerts)
                self.save_records(records, DataGeneral)
                self.logger.info(f"成功抓取并保存 {len(food_alerts)} 条食品警报数据")
                return True
            else:
                self.logger.warning("未抓取到食品警报数据")
                return False
        except Exception as e:
            self.logger.error(f"抓取食品警报数据失败: {e}")
            return False
    
    def get_date_from_days_ago(self):
        """获取指定天数前的时间戳"""
        days_ago = datetime.now() - timedelta(days=self.days)
        timestamp = int(days_ago.timestamp())
        return timestamp
    
    def fetch_api_data(self, page=1, date_from=None):
        """
        获取API数据
        
        Args:
            page (int, optional): 页码，默认为1
            date_from (int, optional): 起始时间戳，默认为None
            
        Returns:
            dict: API响应的JSON数据，如果请求失败则返回None
        """
        if date_from is None:
            date_from = self.get_date_from_days_ago()

        params = {
            'keywords': '',
            'filter_type[Food alert]': 'Food alert',
            'dateFrom': date_from,
            'page': page
        }

        response = self.make_request(
            url=self.base_url,
            method="GET",
            params=params,
            timeout=30,
            verify=False
        )
        
        if not response:
            return None
        
        try:
            return response.json()
        except json.JSONDecodeError:
            self.logger.error("解析API响应JSON失败")
            return None
    
    def extract_nation_labels(self, nation_data):
        """
        从nation数据中提取label字段
        
        Args:
            nation_data: 可能是列表格式，如 [{id: "33", label: "Northern Ireland"}]
            
        Returns:
            list: 提取的国家标签列表
        """
        nations = []

        if isinstance(nation_data, list):
            for nation_item in nation_data:
                if isinstance(nation_item, dict) and 'label' in nation_item:
                    nations.append(nation_item['label'])
        elif isinstance(nation_data, dict) and 'label' in nation_data:
            # 如果是单个字典对象
            nations.append(nation_data['label'])

        return nations
    
    def extract_items_data(self, api_response):
        """
        从API响应中提取所需的数据
        
        Args:
            api_response (dict): API响应的JSON数据
            
        Returns:
            list: 提取的数据项列表
        """
        extracted_items = []

        if not api_response:
            return extracted_items

        # 根据实际API响应结构，数据在#data字段下
        if '#data' in api_response:
            data = api_response['#data']
            if 'items' in data and isinstance(data['items'], list):
                items = data['items']

                for item in items:
                    if isinstance(item, dict):
                        # 提取字段，注意name、intro、body都有#markup子字段
                        name = ''
                        if 'name' in item and isinstance(item['name'], dict) and '#markup' in item['name']:
                            name = item['name']['#markup']

                        intro = ''
                        if 'intro' in item and isinstance(item['intro'], dict) and '#markup' in item['intro']:
                            intro = item['intro']['#markup']

                        body = ''
                        if 'body' in item and isinstance(item['body'], dict) and '#markup' in item['body']:
                            body = item['body']['#markup']

                        # 提取nation字段中的label
                        nations = []
                        if 'nation' in item:
                            nations = self.extract_nation_labels(item['nation'])

                        extracted_item = {
                            'name': name,
                            'intro': intro,
                            'body': body,
                            'filter_type': item.get('filter_type', ''),
                            'content_type': item.get('content_type', ''),
                            'created': item.get('created_formatted', ''),
                            'url': item.get('url', ''),
                            'nations': nations  # 添加提取的国家列表
                        }
                        extracted_items.append(extracted_item)

        return extracted_items
    
    def scrape_all_pages(self):
        """
        抓取所有分页的数据
        
        Returns:
            list: 所有页面的数据项列表
        """
        all_items = []
        page = 1

        # 首先获取第一页数据，确定总数
        first_response = self.fetch_api_data(page=1)
        if not first_response:
            return all_items

        # 提取第一页的数据
        first_page_items = self.extract_items_data(first_response)
        all_items.extend(first_page_items)

        # 获取总数和计算总页数
        total = first_response.get('#data', {}).get('total', 0)
        items_per_page = 10  # 每页10条
        total_pages = (total + items_per_page - 1) // items_per_page  # 向上取整

        self.logger.info(f"发现总共 {total} 条数据，分 {total_pages} 页")

        # 抓取剩余页面
        for page in range(2, total_pages + 1):
            self.logger.info(f"正在抓取第 {page}/{total_pages} 页")
            response = self.fetch_api_data(page=page)

            if response:
                page_items = self.extract_items_data(response)
                all_items.extend(page_items)

                # 添加延迟，避免请求过于频繁
                time.sleep(0.5)
            else:
                self.logger.warning(f"抓取第 {page} 页失败")

        return all_items
    
    def scrape_food_alerts(self):
        """
        抓取食品警报数据的主方法
        
        Returns:
            list: 抓取的食品警报数据列表
        """
        # 抓取所有数据
        all_items = self.scrape_all_pages()
        return all_items
    
    def convert_to_data_general(self, food_alerts):
        """
        将抓取的食品警报数据转换为字典列表，用于创建DataGeneral模型
        
        Args:
            food_alerts (list): 抓取的食品警报数据列表
            
        Returns:
            list: 包含字段映射的字典列表，用于创建DataGeneral模型
        """
        records = []
        
        for alert in food_alerts:
            # 提取国家信息，如果有多个国家，用逗号分隔
            nations_list = alert.get('nations', [])
            nations_str = ', '.join(nations_list) if nations_list else 'United Kingdom'
            
            # 创建包含字段映射的字典
            record = {
                'title': alert.get('name', ''),
                'summary': alert.get('intro', ''),
                'publish_date': alert.get('created', ''),
                'category': '',
                'risk_category': 'Food alert',
                'risk_factor': '',
                'source_url': alert.get('url', ''),
                'country': nations_str,
                'origin_country': nations_str
            }
            records.append(record)
        
        return records
