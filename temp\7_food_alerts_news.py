#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英国食品标准局API数据抓取脚本
抓取https://www.food.gov.uk/search-api的食品警报数据
"""

import requests
import json
import time
from datetime import datetime, timedelta
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class FoodAlertsAPIScraper:
    def __init__(self):
        self.base_url = "https://www.food.gov.uk/search-api"
        self.session = requests.Session()

        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.food.gov.uk/',
        })

    def get_date_from_one_month_ago(self):
        """获取一个月前的时间戳"""
        one_month_ago = datetime.now() - timedelta(days=30)
        timestamp = int(one_month_ago.timestamp())
        return timestamp

    def fetch_api_data(self, page=1, date_from=None):
        """获取API数据"""
        if date_from is None:
            date_from = self.get_date_from_one_month_ago()

        params = {
            'keywords': '',
            'filter_type[Food alert]': 'Food alert',
            'dateFrom': date_from,
            'page': page
        }

        try:
            response = self.session.get(self.base_url, params=params, timeout=30, verify=False)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return None
        except json.JSONDecodeError as e:
            return None

    def extract_nation_labels(self, nation_data):
        """从nation数据中提取label字段

        Args:
            nation_data: 可能是列表格式，如 [{id: "33", label: "Northern Ireland"}]

        Returns:
            list: 提取的国家标签列表
        """
        nations = []

        if isinstance(nation_data, list):
            for nation_item in nation_data:
                if isinstance(nation_item, dict) and 'label' in nation_item:
                    nations.append(nation_item['label'])
        elif isinstance(nation_data, dict) and 'label' in nation_data:
            # 如果是单个字典对象
            nations.append(nation_data['label'])

        return nations

    def extract_items_data(self, api_response):
        """从API响应中提取所需的数据"""
        extracted_items = []

        if not api_response:
            return extracted_items

        # 根据实际API响应结构，数据在#data字段下
        if '#data' in api_response:
            data = api_response['#data']
            if 'items' in data and isinstance(data['items'], list):
                items = data['items']

                for item in items:
                    if isinstance(item, dict):
                        # 提取字段，注意name、intro、body都有#markup子字段
                        name = ''
                        if 'name' in item and isinstance(item['name'], dict) and '#markup' in item['name']:
                            name = item['name']['#markup']

                        intro = ''
                        if 'intro' in item and isinstance(item['intro'], dict) and '#markup' in item['intro']:
                            intro = item['intro']['#markup']

                        body = ''
                        if 'body' in item and isinstance(item['body'], dict) and '#markup' in item['body']:
                            body = item['body']['#markup']

                        # 提取nation字段中的label
                        nations = []
                        if 'nation' in item:
                            nations = self.extract_nation_labels(item['nation'])

                        extracted_item = {
                            'name': name,
                            'intro': intro,
                            'body': body,
                            'filter_type': item.get('filter_type', ''),
                            'content_type': item.get('content_type', ''),
                            'created': item.get('created_formatted', ''),
                            'url': item.get('url', ''),
                            'nations': nations  # 添加提取的国家列表
                        }
                        extracted_items.append(extracted_item)

        return extracted_items

    def scrape_all_pages(self):
        """抓取所有分页的数据"""
        all_items = []
        page = 1

        # 首先获取第一页数据，确定总数
        first_response = self.fetch_api_data(page=1)
        if not first_response:
            return all_items

        # 提取第一页的数据
        first_page_items = self.extract_items_data(first_response)
        all_items.extend(first_page_items)

        # 获取总数和计算总页数
        total = first_response.get('#data', {}).get('total', 0)
        items_per_page = 10  # 每页10条
        total_pages = (total + items_per_page - 1) // items_per_page  # 向上取整

        # 抓取剩余页面
        for page in range(2, total_pages + 1):
            response = self.fetch_api_data(page=page)

            if response:
                page_items = self.extract_items_data(response)
                all_items.extend(page_items)

                # 添加延迟，避免请求过于频繁
                time.sleep(0.5)

        return all_items

    def scrape_food_alerts(self):
        """主要的抓取方法"""
        # 抓取所有数据
        all_items = self.scrape_all_pages()
        return all_items


def main():
    """主函数"""
    scraper = FoodAlertsAPIScraper()

    # 抓取食品警报数据
    print("=== 抓取食品警报数据 ===")
    food_alerts = scraper.scrape_food_alerts()

    # 按照指定格式输出所有数据
    for i, alert in enumerate(food_alerts, 1):
        print(f"--- 警报 {i} ---")
        print(f"任务标题: {alert.get('name', 'N/A')}")
        print(f"任务摘要: {alert.get('intro', 'N/A')}")
        print(f"食品类别: ")  # 该字段在当前API中未提供
        print(f"风险类别: Food alert")
        print(f"风险因子: ")  # 该字段在当前API中未提供
        print(f"发布日期: {alert.get('created', 'N/A')}")
        print(f"原始链接: {alert.get('url', 'N/A')}")

        # 处理nations字段，如果有多个国家用逗号分隔
        nations_list = alert.get('nations', [])
        nations_str = ', '.join(nations_list) if nations_list else 'N/A'
        print(f"信息通报国别: {nations_str}")
        print(f"产地国别: {nations_str}")
        print()


if __name__ == "__main__":
    main()