import logging
import importlib
from models.task_config import TaskConfig
from crawlers.fda_crawler import FDACrawler
from crawlers.rybzszh_crawler import RYBZSZHCrawler
from crawlers.wanfang_magazine_crawler import WfMagazineCrawler
from crawlers.foodmate_crawler import FoodmateCrawler
from crawlers.cfsa_crawler import CFSACrawler
from crawlers.cfs_hk_crawler import CFSHKCrawler
from crawlers.food_alerts_crawler import FoodAlertsCrawler
from crawlers.mpi_food_recalls_crawler import MPIFoodRecallsCrawler

class CaptureService:
    """
    数据抓取服务，用于执行数据抓取任务
    """
    
    # 爬虫类型映射
    CRAWLER_MAPPING = {
        "fda": FDACrawler,
        "rybzszh": RYBZSZHCrawler,
        "wanfang_magazine": WfMagazineCrawler,
        "foodmate": FoodmateCrawler,
        "cfsa": CFSACrawler,
        "cfs_hk": CFSHKCrawler,
        "food_alerts": FoodAlertsCrawler,
        "mpi_food_recalls": MPIFoodRecallsCrawler
    }
    
    # 任务名称到爬虫类型的映射
    TASK_NAME_MAPPING = {
        "美国食品和药物管理局FDA": "fda",
        "乳业标准数字化": "rybzszh",
        "万方期刊数据": "wanfang_magazine",
        "食品伙伴网标准": "foodmate",
        "中国食品安全风险评估中心": "cfsa",
        "香港食物安全中心": "cfs_hk",
        "英国食品标准局": "food_alerts",
        "新西兰初级产业部": "mpi_food_recalls"
    }
    
    def __init__(self):
        """
        初始化数据抓取服务
        """
        self.logger = logging.getLogger("service.capture")
    
    def execute_task(self, task_id):
        """
        执行抓取任务
        
        Args:
            task_id (int): 任务ID
            
        Returns:
            bool: 执行成功返回True，否则返回False
        """
        try:
            # 获取任务配置
            task = TaskConfig.get_by_id(task_id)
            if not task:
                self.logger.error(f"任务不存在: {task_id}")
                return False
                
            # 根据任务名称确定爬虫类型
            crawler_type = self._get_crawler_type(task.task_name)
            if not crawler_type:
                self.logger.error(f"无法确定爬虫类型: {task.task_name}")
                return False
                
            # 创建爬虫实例
            crawler_class = self.CRAWLER_MAPPING.get(crawler_type)
            if not crawler_class:
                self.logger.error(f"爬虫类型不支持: {crawler_type}")
                return False
                
            # 执行爬虫
            crawler = crawler_class(task_id)
            success = crawler.run()
            
            self.logger.info(f"任务执行完成: {task.task_name} (ID: {task.id}), 结果: {'成功' if success else '失败'}")
            return success
        except Exception as e:
            self.logger.error(f"执行抓取任务失败: {e}")
            return False
    
    def _get_crawler_type(self, task_name):
        """
        根据任务名称确定爬虫类型
        
        Args:
            task_name (str): 任务名称
            
        Returns:
            str: 爬虫类型，如果无法确定则返回None
        """
        # 1. 首先尝试从映射表中直接查找任务名称
        if task_name in self.TASK_NAME_MAPPING:
            return self.TASK_NAME_MAPPING[task_name]
        
        # 2. 如果映射表中没有找到，则使用关键词匹配（向后兼容）
        task_name_lower = task_name.lower()
        
        # 根据任务名称关键字确定爬虫类型
        if "fda" in task_name_lower:
            return "fda"
        elif "食品抽检" in task_name_lower or "rybzszh" in task_name_lower:
            return "rybzszh"
        elif "万方" in task_name_lower or "期刊" in task_name_lower or "wanfang" in task_name_lower:
            return "wanfang_magazine"
        elif "食品伙伴" in task_name_lower or "foodmate" in task_name_lower:
            return "foodmate"
        elif "食品安全风险评估" in task_name_lower or "cfsa" in task_name_lower:
            return "cfsa"
        elif "香港食物安全" in task_name_lower or "cfs_hk" in task_name_lower:
            return "cfs_hk"
        elif "英国食品标准局" in task_name_lower or "food_alerts" in task_name_lower or "fsa" in task_name_lower:
            return "food_alerts"
        elif "新西兰" in task_name_lower or "mpi" in task_name_lower or "food_recalls" in task_name_lower:
            return "mpi_food_recalls"
        
        # 默认返回None，表示无法确定
        return None
    
    def get_available_crawlers(self):
        """
        获取可用的爬虫类型
        
        Returns:
            list: 爬虫类型列表
        """
        return list(self.CRAWLER_MAPPING.keys())
    
    def run_crawler(self, crawler_type, task_id=None, **kwargs):
        """
        运行指定类型的爬虫
        
        Args:
            crawler_type (str): 爬虫类型
            task_id (int, optional): 任务ID，如果不提供则使用临时ID
            **kwargs: 传递给爬虫的其他参数
            
        Returns:
            bool: 运行成功返回True，否则返回False
        """
        try:
            # 检查爬虫类型是否支持
            crawler_class = self.CRAWLER_MAPPING.get(crawler_type)
            if not crawler_class:
                self.logger.error(f"爬虫类型不支持: {crawler_type}")
                return False
                
            # 如果没有提供任务ID，则使用临时ID
            if task_id is None:
                task_id = 999  # 临时ID
                
            # 创建爬虫实例
            crawler = crawler_class(task_id, **kwargs)
            success = crawler.run()
            
            self.logger.info(f"爬虫运行完成: {crawler_type}, 结果: {'成功' if success else '失败'}")
            return success
        except Exception as e:
            self.logger.error(f"运行爬虫失败: {e}")
            return False 