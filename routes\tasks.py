"""
任务管理相关路由
"""
import logging
from flask import Blueprint, render_template, request, redirect, url_for, flash
from models.task_config import TaskConfig
from services.scheduler_service import SchedulerService
from services.capture_service import CaptureService

# 创建蓝图
tasks_bp = Blueprint('tasks', __name__)

@tasks_bp.route('/tasks')
def task_list():
    """任务列表"""
    tasks = TaskConfig.get_all(include_disabled=True)
    
    # 获取任务状态
    scheduler_service = SchedulerService.get_instance()
    task_status = {}
    for task in tasks:
        status = scheduler_service.get_task_status(task.id)
        if status:
            task_status[task.id] = status
    
    return render_template("task_list.html", tasks=tasks, task_status=task_status)

@tasks_bp.route('/tasks/form', methods=['GET', 'POST'])
@tasks_bp.route('/tasks/form/<int:task_id>', methods=['GET', 'POST'])
def task_form(task_id=None):
    """任务表单"""
    task = None
    if task_id:
        task = TaskConfig.get_by_id(task_id)
        if not task:
            flash("任务不存在", "danger")
            return redirect(url_for('tasks.task_list'))
    
    # 定义预设任务模板
    task_templates = {
        "fda": [
            "美国食品和药物管理局FDA"
        ],
        "rybzszh": [
            "乳业标准数字化"
        ],
        "wanfang_magazine": [
            "万方期刊数据"
        ],
        "foodmate": [
            "食品伙伴网标准"
        ],
        "cfsa": [
            "中国食品安全风险评估中心"
        ],
        "cfs_hk": [
            "香港食物安全中心"
        ],
        "food_alerts": [
            "英国食品标准局"
        ],
        "mpi_food_recalls": [
            "新西兰初级产业部"
        ]
    }
    
    # 获取可用的爬虫类型
    capture_service = CaptureService()
    available_crawlers = capture_service.get_available_crawlers()
    
    if request.method == 'POST':
        task_name = request.form.get('task_name')
        schedule_cron = request.form.get('schedule_cron')
        is_enabled = 1 if request.form.get('is_enabled') else 0
        
        if not task:
            task = TaskConfig(
                task_name=task_name,
                schedule_cron=schedule_cron,
                is_enabled=is_enabled
            )
        else:
            task.task_name = task_name
            task.schedule_cron = schedule_cron
            task.is_enabled = is_enabled
        
        if task.save():
            # 更新调度器
            scheduler_service = SchedulerService.get_instance()
            if task.is_enabled:
                scheduler_service.update_task(task)
            else:
                scheduler_service.remove_task(task.id)
                
            flash(f"任务已{'更新' if task_id else '创建'}", "success")
            return redirect(url_for('tasks.task_list'))
        else:
            flash(f"任务{'更新' if task_id else '创建'}失败", "danger")
    
    return render_template("task_form.html", task=task, task_templates=task_templates, available_crawlers=available_crawlers)

@tasks_bp.route('/tasks/toggle/<int:task_id>')
def task_toggle(task_id):
    """切换任务状态"""
    task = TaskConfig.get_by_id(task_id)
    if not task:
        flash("任务不存在", "danger")
        return redirect(url_for('tasks.task_list'))
    
    task.is_enabled = 1 if task.is_enabled == 0 else 0
    if task.save():
        scheduler_service = SchedulerService.get_instance()
        if task.is_enabled:
            scheduler_service.update_task(task)
            flash("任务已启用", "success")
        else:
            scheduler_service.remove_task(task.id)
            flash("任务已禁用", "warning")
    else:
        flash("操作失败", "danger")
    
    return redirect(url_for('tasks.task_list'))

@tasks_bp.route('/tasks/run/<int:task_id>')
def task_run(task_id):
    """立即运行任务"""
    task = TaskConfig.get_by_id(task_id)
    if not task:
        flash("任务不存在", "danger")
        return redirect(url_for('tasks.task_list'))
    
    scheduler_service = SchedulerService.get_instance()
    if scheduler_service.run_task_now(task_id):
        flash("任务已提交执行", "success")
    else:
        flash("任务执行失败", "danger")
    
    return redirect(url_for('tasks.task_list'))

@tasks_bp.route('/tasks/delete/<int:task_id>')
def task_delete(task_id):
    """删除任务"""
    task = TaskConfig.get_by_id(task_id)
    if not task:
        flash("任务不存在", "danger")
        return redirect(url_for('tasks.task_list'))
    
    # 先从调度器中移除
    scheduler_service = SchedulerService.get_instance()
    scheduler_service.remove_task(task_id)
    
    # 然后从数据库中删除
    if task.delete():
        flash("任务已删除", "success")
    else:
        flash("任务删除失败", "danger")
    
    return redirect(url_for('tasks.task_list')) 