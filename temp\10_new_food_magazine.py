#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
New Food Magazine数据抓取脚本
使用Selenium抓取https://www.newfoodmagazine.com/core_topic/food-safety/页面的文章数据
"""

import re
import time
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


class NewFoodMagazineScraper:
    def __init__(self):
        self.base_url = self.build_url_with_date_range()
        self.driver = None
        self.setup_driver()

    def build_url_with_date_range(self):
        """构建包含动态日期范围的URL"""
        # 获取当前日期
        today = datetime.now()
        # 计算一个月前的日期
        one_month_ago = today - timedelta(days=30)

        # 格式化日期为YYYY-MM-DD格式
        start_date = one_month_ago.strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')

        # 构建URL
        base_url = "https://www.newfoodmagazine.com/core_topic/food-safety/"
        date_range = f"fwp_date_range={start_date}%2C{end_date}"
        full_url = f"{base_url}?{date_range}"

        return full_url

    def setup_driver(self):
        options = Options()
        # options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        self.driver = webdriver.Chrome(options=options)

        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
              get: () => undefined
            });
            """
        })

    def extract_article_data(self, article_element):
        try:
            title_link = article_element.find('h3').find('a') if article_element.find('h3') else None
            if not title_link:
                return None

            title = title_link.get_text(strip=True)
            article_url = title_link.get('href', '')

            # 查找所有的meta段落，因为可能有多个
            meta_ps = article_element.find_all('p', class_='meta')
            date = ''
            author = ''

            # 遍历所有meta段落，找到包含内容的那个
            for meta_p in meta_ps:
                meta_text = meta_p.get_text(strip=True)
                if meta_text:  # 只处理非空的meta段落
                    if '|' in meta_text:
                        parts = meta_text.split('|')
                        if len(parts) >= 2:
                            date = parts[0].strip()

                            # 尝试从<a>标签中提取作者名
                            author_link = meta_p.find('a')
                            if author_link:
                                author = author_link.get_text(strip=True)
                            else:
                                # 如果没有<a>标签，从文本中提取
                                author_part = parts[1].strip()
                                if author_part.startswith('By '):
                                    author = author_part[3:].strip()
                    else:
                        date = meta_text
                    break  # 找到有内容的meta段落后就退出循环

            excerpt = ''
            excerpt_p = article_element.find('p', class_='listOnly')
            if excerpt_p:
                excerpt = excerpt_p.get_text(strip=True)

            return {
                'title': title,
                'date': date,
                'author': author,
                'excerpt': excerpt,
                'url': article_url
            }

        except Exception as e:
            return None

    def get_total_pages(self):
        try:
            self.driver.get(self.base_url)
            time.sleep(3)

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 查找所有包含facetwp-pager类的div
            pagers = soup.find_all('div', class_='facetwp-pager')

            page_numbers = []

            for pager in pagers:
                # 查找所有包含facetwp-page类的a标签
                page_links = pager.find_all('a', class_='facetwp-page')

                for link in page_links:
                    classes = link.get('class', [])

                    # 跳过next/prev链接，但包含first/last链接
                    if 'next' in classes or 'prev' in classes:
                        continue

                    page_num = link.get('data-page')
                    if page_num and page_num.isdigit():
                        page_numbers.append(int(page_num))

            if page_numbers:
                total_pages = max(page_numbers)
                return total_pages

            return 1

        except Exception as e:
            return 1

    def scrape_page(self, page_num):
        try:
            if page_num == 1:
                url = self.base_url
            else:
                url = f"{self.base_url}&fwp_paged={page_num}"

            self.driver.get(url)
            time.sleep(3)

            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "article"))
                )
            except:
                pass

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            articles = soup.find_all('article')

            article_data = []
            for article in articles:
                data = self.extract_article_data(article)
                if data:
                    article_data.append(data)

            return article_data

        except Exception as e:
            return []

    def scrape_all_articles(self, max_pages=None):
        all_articles = []

        try:
            total_pages = self.get_total_pages()

            if max_pages:
                total_pages = min(total_pages, max_pages)

            for page in range(1, total_pages + 1):
                articles = self.scrape_page(page)
                all_articles.extend(articles)

                if len(articles) == 0 and page > 1:
                    break

                if page < total_pages:
                    time.sleep(2)

        except KeyboardInterrupt:
            pass
        except Exception as e:
            pass

        return all_articles

    def close(self):
        if self.driver:
            self.driver.quit()


def main():
    MAX_PAGES = None  # 设置为2测试分页功能
    scraper = NewFoodMagazineScraper()

    try:
        articles = scraper.scrape_all_articles(max_pages=MAX_PAGES)

        for i, article in enumerate(articles, 1):
            print(f"任务标题: {article['title']}")
            print(f"任务摘要: {article['excerpt']}")
            print(f"食品类别: ")
            print(f"风险类别: ")
            print(f"风险因子: ")
            print(f"发布日期: {article['date']}")
            print(f"原始链接: {article['url']}")
            print(f"信息通报国别: ")
            print(f"产地国别: ")
            print("-" * 60)

    finally:
        scraper.close()


if __name__ == "__main__":
    main()