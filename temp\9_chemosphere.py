#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chemosphere期刊文章数据抓取脚本
使用Selenium抓取https://www.sciencedirect.com/journal/chemosphere页面的文章数据
提取文章标题、日期和链接
"""

import re
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


def setup_driver():
    """设置Chrome浏览器驱动"""
    # 设置 headless Chrome
    options = Options()
    # options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument(
        "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

    driver = webdriver.Chrome(options=options)

    # 反反爬设置
    driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
        "source": """
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined
        });
        """
    })
    return driver


def extract_article_data(driver, url):
    """提取文章数据"""
    try:
        print(f"正在访问: {url}")
        driver.get(url)

        # 等待页面加载
        wait = WebDriverWait(driver, 20)
        wait.until(EC.presence_of_element_located((By.ID, "latest-published-articles")))

        # 等待额外时间确保内容完全加载
        time.sleep(3)

        # 获取页面源码
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')

        # 查找文章容器
        articles_container = soup.find('div', {'id': 'latest-published-articles'})
        if not articles_container:
            print("未找到文章容器")
            return []

        # 查找所有文章项
        article_items = articles_container.find_all('div', class_='js-article-item')
        print(f"找到 {len(article_items)} 篇文章")

        articles_data = []

        for item in article_items:
            try:
                # 提取标题和链接
                title_link = item.find('a', class_='js-article__item__title__link')
                if not title_link:
                    continue

                title = title_link.find('span', class_='anchor-text')
                if title:
                    title_text = title.get_text(strip=True)
                else:
                    continue

                # 提取链接
                href = title_link.get('href', '')
                if href.startswith('/'):
                    full_link = f"https://www.sciencedirect.com{href}"
                else:
                    full_link = href

                # 提取日期
                date_element = item.find('span', class_='js-article-item-date')
                date_text = date_element.get_text(strip=True) if date_element else "未知日期"

                article_data = {
                    'title': title_text,
                    'date': date_text,
                    'link': full_link
                }

                articles_data.append(article_data)
                print(f"提取文章: {title_text[:50]}...")

            except Exception as e:
                print(f"提取单篇文章数据时出错: {e}")
                continue

        return articles_data

    except Exception as e:
        print(f"提取数据时出错: {e}")
        return []


def main():
    """主函数"""
    url = "https://www.sciencedirect.com/journal/chemosphere"

    driver = None
    try:
        # 设置浏览器驱动
        driver = setup_driver()

        # 提取文章数据
        articles_data = extract_article_data(driver, url)

        if articles_data:
            print(f"\n成功提取 {len(articles_data)} 篇文章数据")
            print("=" * 80)

            # 打印所有文章的信息
            for i, article in enumerate(articles_data, 1):
                print(f"\n文章 {i}:")
                print(f"标题: {article['title']}")
                print(f"日期: {article['date']}")
                print(f"链接: {article['link']}")
                print("-" * 60)

        else:
            print("未能提取到文章数据")

    except Exception as e:
        print(f"程序执行出错: {e}")

    finally:
        if driver:
            driver.quit()
            print("浏览器已关闭")


if __name__ == "__main__":
    main()
