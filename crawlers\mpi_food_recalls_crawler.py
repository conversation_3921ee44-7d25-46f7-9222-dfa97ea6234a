#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新西兰初级产业部食品召回页面抓取爬虫
抓取https://www.mpi.govt.nz/food-safety-home/food-recalls-and-complaints/recalled-food-products/页面的召回产品信息
"""

import logging
import time
import re
from urllib.parse import urljoin
import urllib3
from bs4 import BeautifulSoup
from crawlers.base_crawler import BaseCrawler
from models.data_general import DataGeneral
from config import Config

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class MPIFoodRecallsCrawler(BaseCrawler):
    """
    新西兰初级产业部食品召回数据爬虫
    """
    
    def __init__(self, task_id, batch_id=None, days=None):
        """
        初始化爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
            days (int, optional): 抓取最近多少天的数据，默认使用配置值
        """
        super().__init__(task_id, batch_id)
        self.days = days or Config.CAPTURE_DAYS
        self.logger = logging.getLogger("crawler.MPIFoodRecallsCrawler")
        
        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 禁用SSL验证
        self.session.verify = False
        
        # 基础URL和目标URL
        self.base_url = "https://www.mpi.govt.nz"
        self.target_url = "https://www.mpi.govt.nz/food-safety-home/food-recalls-and-complaints/recalled-food-products/"
    
    def _crawl(self):
        """
        执行抓取逻辑
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            # 抓取食品召回数据
            self.logger.info("开始抓取新西兰初级产业部食品召回数据")
            recalls = self.scrape_recalls()
            
            if recalls:
                # 将数据转换为DataGeneral模型并保存
                records = self.convert_to_data_general(recalls)
                self.save_records(records, DataGeneral)
                self.logger.info(f"成功抓取并保存 {len(recalls)} 条食品召回数据")
                return True
            else:
                self.logger.warning("未抓取到食品召回数据")
                return False
        except Exception as e:
            self.logger.error(f"抓取食品召回数据失败: {e}")
            return False
    
    def fetch_page(self, url):
        """
        获取网页内容
        
        Args:
            url (str): 网页URL
            
        Returns:
            str: 网页内容，如果请求失败则返回None
        """
        response = self.make_request(
            url=url,
            method="GET",
            timeout=30,
            verify=False
        )
        
        if not response:
            return None
        
        response.encoding = 'utf-8'
        return response.text
    
    def parse_recall_detail(self, html_content):
        """
        解析召回产品详情页面，提取日期和摘要
        
        Args:
            html_content (str): 详情页面的HTML内容
            
        Returns:
            tuple: (日期, 摘要)
        """
        if not html_content:
            return '', ''
            
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找包含日期和摘要的div.wrapper.intro
        intro_wrapper = soup.find('div', class_='wrapper intro')

        date = ''
        summary = ''

        if intro_wrapper:
            # 查找strapline-intro div
            strapline_intro = intro_wrapper.find('div', class_='strapline-intro')
            if strapline_intro:
                # 获取p标签中的文本
                p_tag = strapline_intro.find('p')
                if p_tag:
                    full_text = p_tag.get_text().strip()

                    # 尝试提取日期（格式：DD Month YYYY）
                    date_pattern = r'(\d{1,2}\s+\w+\s+\d{4})'
                    date_match = re.search(date_pattern, full_text)
                    if date_match:
                        date = date_match.group(1)
                        # 提取日期后的内容作为摘要
                        summary = full_text[date_match.end():].strip()
                        # 移除开头的冒号和空格
                        if summary.startswith(':'):
                            summary = summary[1:].strip()
                    else:
                        # 如果没有找到日期，整个文本作为摘要
                        summary = full_text

        return date, summary
    
    def _find_year_header(self, ul_element):
        """
        查找ul元素前面的年份标题
        
        Args:
            ul_element: BeautifulSoup对象，表示ul元素
            
        Returns:
            str: 年份标题
        """
        prev_sibling = ul_element.find_previous_sibling()
        while prev_sibling:
            if prev_sibling.name == 'h2':
                return prev_sibling.get_text().strip()
            prev_sibling = prev_sibling.find_previous_sibling()
        return ''
    
    def _build_full_url(self, href):
        """
        构建完整的URL
        
        Args:
            href (str): 相对或绝对URL
            
        Returns:
            str: 完整的URL
        """
        if not href:
            return ''
        return href if href.startswith('http') else urljoin(self.base_url, href)
    
    def _create_recall_item(self, year_header, product_name, link_url=''):
        """
        创建召回产品项目
        
        Args:
            year_header (str): 年份标题
            product_name (str): 产品名称
            link_url (str, optional): 产品详情链接
            
        Returns:
            dict: 召回产品项目
        """
        return {
            'year': year_header,
            'product_name': product_name,
            'link': link_url,
            'date': '',
            'summary': ''
        }
    
    def parse_recall_content(self, html_content):
        """
        解析召回产品内容
        
        Args:
            html_content (str): 主页面的HTML内容
            
        Returns:
            list: 召回产品项目列表
        """
        if not html_content:
            return []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            recall_items = []

            # 查找所有包含wrapper optional-sidebar类的div
            wrapper_divs = soup.find_all('div', class_='wrapper optional-sidebar')

            if not wrapper_divs:
                return []

            for wrapper_div in wrapper_divs:
                richtext_div = wrapper_div.find('div', class_='richtext')
                if not richtext_div:
                    continue

                # 查找所有ul元素
                ul_elements = richtext_div.find_all('ul')

                for ul in ul_elements:
                    year_header = self._find_year_header(ul)
                    li_elements = ul.find_all('li')

                    for li in li_elements:
                        text = li.get_text().strip()
                        if not text:
                            continue

                        link = li.find('a')
                        if link:
                            # 有链接的产品
                            href = link.get('href', '')
                            product_name = link.get_text().strip()
                            full_url = self._build_full_url(href)
                            recall_item = self._create_recall_item(year_header, product_name, full_url)
                        else:
                            # 无链接的产品
                            recall_item = self._create_recall_item(year_header, text)

                        recall_items.append(recall_item)

            return recall_items

        except Exception as e:
            self.logger.error(f"解析召回产品内容失败: {e}")
            return []
    
    def _fetch_product_details(self, item):
        """
        获取单个产品的详细信息
        
        Args:
            item (dict): 召回产品项目
            
        Returns:
            None
        """
        if not item['link']:
            return
        
        try:
            detail_html = self.fetch_page(item['link'])
            if detail_html:
                date, summary = self.parse_recall_detail(detail_html)
                item['date'] = date
                item['summary'] = summary
        except Exception as e:
            self.logger.error(f"获取产品详细信息失败: {e}")
    
    def scrape_recalls(self):
        """
        抓取食品召回数据的主方法
        
        Returns:
            list: 抓取的食品召回数据列表
        """
        # 获取主页面内容
        html_content = self.fetch_page(self.target_url)
        if not html_content:
            self.logger.error("获取主页面内容失败")
            return []
        
        # 解析召回产品信息
        recall_items = self.parse_recall_content(html_content)
        
        if not recall_items:
            self.logger.warning("未找到任何召回产品信息")
            return []
        
        # 为每个有链接的召回产品获取详细信息
        items_with_links = [item for item in recall_items if item['link']]
        self.logger.info(f"找到 {len(recall_items)} 个召回产品，其中 {len(items_with_links)} 个有详细信息")
        
        for index, item in enumerate(recall_items):
            if item['link']:
                self.logger.info(f"正在获取第 {index+1}/{len(items_with_links)} 个产品的详细信息")
                self._fetch_product_details(item)
                # 添加延时避免请求过于频繁
                time.sleep(1)
        
        return recall_items
    
    def convert_to_data_general(self, recalls):
        """
        将抓取的食品召回数据转换为字典列表，用于创建DataGeneral模型
        
        Args:
            recalls (list): 抓取的食品召回数据列表
            
        Returns:
            list: 包含字段映射的字典列表，用于创建DataGeneral模型
        """
        records = []
        
        for recall in recalls:
            # 创建包含字段映射的字典
            record = {
                'title': recall.get('product_name', ''),
                'summary': recall.get('summary', ''),
                'publish_date': recall.get('date', ''),
                'category': '',
                'risk_category': 'recalled food products',
                'risk_factor': '',
                'source_url': recall.get('link', ''),
                'country': 'New Zealand',
                'origin_country': 'New Zealand'
            }
            records.append(record)
        
        return records
