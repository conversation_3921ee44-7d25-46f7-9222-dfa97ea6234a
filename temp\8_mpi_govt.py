#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新西兰初级产业部食品召回页面抓取脚本
抓取https://www.mpi.govt.nz/food-safety-home/food-recalls-and-complaints/recalled-food-products/页面的召回产品信息
"""

import requests
from bs4 import BeautifulSoup
import time
import re
from urllib.parse import urljoin
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class MPIFoodRecallsScraper:
    def __init__(self):
        self.base_url = "https://www.mpi.govt.nz"
        self.target_url = "https://www.mpi.govt.nz/food-safety-home/food-recalls-and-complaints/recalled-food-products/"
        self.session = requests.Session()

        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def fetch_page(self, url):
        """获取网页内容"""
        try:
            response = self.session.get(url, timeout=30, verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            return None

    def parse_recall_detail(self, html_content):
        """解析召回产品详情页面，提取日期和摘要"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找包含日期和摘要的div.wrapper.intro
        intro_wrapper = soup.find('div', class_='wrapper intro')

        date = ''
        summary = ''

        if intro_wrapper:
            # 查找strapline-intro div
            strapline_intro = intro_wrapper.find('div', class_='strapline-intro')
            if strapline_intro:
                # 获取p标签中的文本
                p_tag = strapline_intro.find('p')
                if p_tag:
                    full_text = p_tag.get_text().strip()

                    # 尝试提取日期（格式：DD Month YYYY）
                    date_pattern = r'(\d{1,2}\s+\w+\s+\d{4})'
                    date_match = re.search(date_pattern, full_text)
                    if date_match:
                        date = date_match.group(1)
                        # 提取日期后的内容作为摘要
                        summary = full_text[date_match.end():].strip()
                        # 移除开头的冒号和空格
                        if summary.startswith(':'):
                            summary = summary[1:].strip()
                    else:
                        # 如果没有找到日期，整个文本作为摘要
                        summary = full_text

        return date, summary

    def _find_year_header(self, ul_element):
        """查找ul元素前面的年份标题"""
        prev_sibling = ul_element.find_previous_sibling()
        while prev_sibling:
            if prev_sibling.name == 'h2':
                return prev_sibling.get_text().strip()
            prev_sibling = prev_sibling.find_previous_sibling()
        return ''

    def _build_full_url(self, href):
        """构建完整的URL"""
        if not href:
            return ''
        return href if href.startswith('http') else urljoin(self.base_url, href)

    def _create_recall_item(self, year_header, product_name, link_url=''):
        """创建召回产品项目"""
        return {
            'year': year_header,
            'product_name': product_name,
            'link': link_url,
            'date': '',
            'summary': ''
        }

    def parse_recall_content(self, html_content):
        """解析召回产品内容"""
        if not html_content:
            return []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            recall_items = []

            # 查找所有包含wrapper optional-sidebar类的div
            wrapper_divs = soup.find_all('div', class_='wrapper optional-sidebar')

            if not wrapper_divs:
                return []

            for wrapper_div in wrapper_divs:
                richtext_div = wrapper_div.find('div', class_='richtext')
                if not richtext_div:
                    continue

                # 查找所有ul元素
                ul_elements = richtext_div.find_all('ul')

                for ul in ul_elements:
                    year_header = self._find_year_header(ul)
                    li_elements = ul.find_all('li')

                    for li in li_elements:
                        text = li.get_text().strip()
                        if not text:
                            continue

                        link = li.find('a')
                        if link:
                            # 有链接的产品
                            href = link.get('href', '')
                            product_name = link.get_text().strip()
                            full_url = self._build_full_url(href)
                            recall_item = self._create_recall_item(year_header, product_name, full_url)
                        else:
                            # 无链接的产品
                            recall_item = self._create_recall_item(year_header, text)

                        recall_items.append(recall_item)

            return recall_items

        except Exception as e:
            return []

    def _fetch_product_details(self, item, index, total):
        """获取单个产品的详细信息"""
        if not item['link']:
            return

        try:
            detail_html = self.fetch_page(item['link'])
            if detail_html:
                date, summary = self.parse_recall_detail(detail_html)
                item['date'] = date
                item['summary'] = summary
        except Exception as e:
            pass

    def scrape_recalls(self):
        """主要的抓取方法"""
        # 获取主页面内容
        html_content = self.fetch_page(self.target_url)
        if not html_content:
            return []

        # 解析召回产品信息
        recall_items = self.parse_recall_content(html_content)

        if not recall_items:
            return []

        # 为每个有链接的召回产品获取详细信息
        items_with_links = [item for item in recall_items if item['link']]
        for index, item in enumerate(recall_items):
            if item['link']:
                self._fetch_product_details(item, index, len(items_with_links))
                # 添加延时避免请求过于频繁
                time.sleep(1)

        return recall_items


def main():
    """主函数"""
    print("开始抓取新西兰MPI食品召回信息...")

    scraper = MPIFoodRecallsScraper()

    # 抓取召回产品信息
    print("正在获取主页面内容...")
    recalls = scraper.scrape_recalls()

    if not recalls:
        print("未找到任何召回产品信息")
        return

    # 统计信息
    items_with_links = [item for item in recalls if item['link']]
    print(f"找到 {len(recalls)} 个召回产品，其中 {len(items_with_links)} 个有详细信息")

    if items_with_links:
        print("正在获取详细信息...")

    # 按照指定格式输出所有召回产品
    print("\n" + "="*60)
    print("召回产品信息:")
    print("="*60)

    for i, recall in enumerate(recalls, 1):
        print(f"\n[{i}]")
        print(f"任务标题: {recall.get('product_name', '')}")
        print(f"任务摘要: {recall.get('summary', '')}")
        print("食品类别: ")
        print("风险类别: recalled food products")
        print("风险因子: ")
        print(f"发布日期: {recall.get('date', '')}")
        print(f"原始链接: {recall.get('link', '')}")
        print("信息通报国别: New Zealand")
        print("产地国别: New Zealand")
        print("-" * 50)  # 分隔线

    print(f"\n抓取完成！共获取 {len(recalls)} 个召回产品信息")


if __name__ == "__main__":
    main()